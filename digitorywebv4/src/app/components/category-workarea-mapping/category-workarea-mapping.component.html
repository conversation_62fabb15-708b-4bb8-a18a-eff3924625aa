<div class="category-mapping-container" [class.dialog-mode]="showAsDialog">
  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-container">
    <mat-spinner diameter="40"></mat-spinner>
  </div>

  <!-- Main Content -->
  <div *ngIf="!isLoading" class="mapping-content">
    <!-- Validation Errors -->
    <div *ngIf="hasValidationErrors" class="validation-errors">
      <mat-icon class="error-icon">error</mat-icon>
      <div class="error-list">
        <p *ngFor="let error of validationErrors" class="error-message">{{ error }}</p>
      </div>
    </div>

    <!-- Two Panel Layout -->
    <div class="two-panel-layout">
      <!-- Category Selection Section (Left Panel) -->
      <div class="category-selection-section">
        <div class="panel-header">
          <mat-icon>category</mat-icon>
          <span>Select Category</span>
        </div>

        <!-- Loading State -->
        <div *ngIf="categories.length === 0" class="loading-state">
          <mat-spinner diameter="24"></mat-spinner>
          <p>Loading categories...</p>
        </div>

        <!-- Category Selection -->
        <mat-form-field *ngIf="categories.length > 0" appearance="outline" class="category-select-field">
          <mat-select
            [(value)]="selectedCategory"
            (selectionChange)="onCategorySelectionChange($event.value)"
            placeholder="Select a category">
            <mat-option *ngFor="let category of categories; trackBy: trackByCategory" [value]="category">
              {{ category }}
            </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <!-- WorkArea Configuration Section (Right Panel) -->
      <div class="workarea-configuration-section">
        <div class="panel-header">
          <mat-icon>work</mat-icon>
          <span>Configure Work Areas for Category</span>
        </div>

        <!-- Empty State Message -->
        <div *ngIf="!selectedCategory" class="empty-state-message">
          <mat-icon class="empty-icon">arrow_back</mat-icon>
          <p>Select a category from the left to configure work area mappings</p>
        </div>

        <!-- WorkArea Configuration -->
        <div *ngIf="selectedCategory" class="workarea-content">
          <div class="selected-category-info">
            <strong>{{ selectedCategory }}</strong>
          </div>

          <mat-form-field appearance="outline" class="workarea-field">
            <mat-select
              [value]="getSelectedWorkAreasForCategory(selectedCategory)"
              (selectionChange)="onWorkAreasChange(selectedCategory, $event.value)"
              multiple
              placeholder="Select work areas">
              <mat-option *ngFor="let workArea of getAvailableWorkAreas(selectedCategory); trackBy: trackByWorkArea" [value]="workArea">
                {{ workArea }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </div>
    </div>

    <!-- Summary Section -->
    <div *ngIf="mappings.length > 0" class="mapping-summary">
      <h3>Current Mappings Summary</h3>
      <div class="summary-grid">
        <div *ngFor="let mapping of mappings" class="summary-item">
          <div class="summary-category">{{ mapping.categoryName }}</div>
          <div class="summary-workareas">
            <span class="workarea-count">{{ mapping.workAreas.length }} work areas</span>
            <div class="workarea-list">
              {{ mapping.workAreas.join(', ') }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Actions -->
  <div class="mapping-actions">
    <button
      mat-button
      type="button"
      (click)="onClose()"
      *ngIf="showAsDialog"
      class="cancel-button"
    >
      Cancel
    </button>

    <button
      mat-raised-button
      color="primary"
      type="button"
      (click)="saveMappings()"
      [disabled]="!canSave"
      class="save-button"
    >
      <mat-spinner *ngIf="isSaving" diameter="20" class="button-spinner"></mat-spinner>
      <mat-icon *ngIf="!isSaving">save</mat-icon>
      {{ isSaving ? 'Saving...' : 'Save Mappings' }}
    </button>
  </div>
</div>
